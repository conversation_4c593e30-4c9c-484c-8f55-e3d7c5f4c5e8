-- Migration to add user isolation to all main models
-- This ensures each user only sees their own data

-- Add userId column to family_members table
ALTER TABLE "family_members" ADD COLUMN "user_id" TEXT;

-- Add userId column to accounts table  
ALTER TABLE "accounts" ADD COLUMN "user_id" TEXT;

-- Add userId column to categories table
ALTER TABLE "categories" ADD COLUMN "user_id" TEXT;

-- Add userId column to tags table
ALTER TABLE "tags" ADD COLUMN "user_id" TEXT;

-- Add userId column to transactions table
ALTER TABLE "transactions" ADD COLUMN "user_id" TEXT;

-- Add userId column to budgets table
ALTER TABLE "budgets" ADD COLUMN "user_id" TEXT;

-- Add userId column to goals table
ALTER TABLE "goals" ADD COLUMN "user_id" TEXT;

-- Add userId column to insights table
ALTER TABLE "insights" ADD COLUMN "user_id" TEXT;

-- Get the first user ID to assign existing data to
-- This assumes there's at least one user in the system
DO $$
DECLARE
    first_user_id TEXT;
BEGIN
    -- Get the first user ID (oldest user)
    SELECT id INTO first_user_id FROM users ORDER BY created_at ASC LIMIT 1;
    
    -- If we have a user, assign all existing data to them
    IF first_user_id IS NOT NULL THEN
        UPDATE "family_members" SET "user_id" = first_user_id WHERE "user_id" IS NULL;
        UPDATE "accounts" SET "user_id" = first_user_id WHERE "user_id" IS NULL;
        UPDATE "categories" SET "user_id" = first_user_id WHERE "user_id" IS NULL;
        UPDATE "tags" SET "user_id" = first_user_id WHERE "user_id" IS NULL;
        UPDATE "transactions" SET "user_id" = first_user_id WHERE "user_id" IS NULL;
        UPDATE "budgets" SET "user_id" = first_user_id WHERE "user_id" IS NULL;
        UPDATE "goals" SET "user_id" = first_user_id WHERE "user_id" IS NULL;
        UPDATE "insights" SET "user_id" = first_user_id WHERE "user_id" IS NULL;
    END IF;
END $$;

-- Make userId columns NOT NULL after assigning existing data
ALTER TABLE "family_members" ALTER COLUMN "user_id" SET NOT NULL;
ALTER TABLE "accounts" ALTER COLUMN "user_id" SET NOT NULL;
ALTER TABLE "categories" ALTER COLUMN "user_id" SET NOT NULL;
ALTER TABLE "tags" ALTER COLUMN "user_id" SET NOT NULL;
ALTER TABLE "transactions" ALTER COLUMN "user_id" SET NOT NULL;
ALTER TABLE "budgets" ALTER COLUMN "user_id" SET NOT NULL;
ALTER TABLE "goals" ALTER COLUMN "user_id" SET NOT NULL;
ALTER TABLE "insights" ALTER COLUMN "user_id" SET NOT NULL;

-- Add foreign key constraints
ALTER TABLE "family_members" ADD CONSTRAINT "family_members_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "accounts" ADD CONSTRAINT "accounts_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "categories" ADD CONSTRAINT "categories_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "tags" ADD CONSTRAINT "tags_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "transactions" ADD CONSTRAINT "transactions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "budgets" ADD CONSTRAINT "budgets_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "goals" ADD CONSTRAINT "goals_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "insights" ADD CONSTRAINT "insights_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Add indexes for better performance on user-based queries
CREATE INDEX "family_members_user_id_idx" ON "family_members"("user_id");
CREATE INDEX "accounts_user_id_idx" ON "accounts"("user_id");
CREATE INDEX "categories_user_id_idx" ON "categories"("user_id");
CREATE INDEX "tags_user_id_idx" ON "tags"("user_id");
CREATE INDEX "transactions_user_id_idx" ON "transactions"("user_id");
CREATE INDEX "budgets_user_id_idx" ON "budgets"("user_id");
CREATE INDEX "goals_user_id_idx" ON "goals"("user_id");
CREATE INDEX "insights_user_id_idx" ON "insights"("user_id");

-- Update unique constraints to include userId where needed
-- Tags should be unique per user
ALTER TABLE "tags" DROP CONSTRAINT "tags_name_key";
ALTER TABLE "tags" ADD CONSTRAINT "tags_name_user_id_key" UNIQUE ("name", "user_id");

-- Budget constraints should include userId
ALTER TABLE "budgets" DROP CONSTRAINT "budgets_categoryId_familyMemberId_month_year_key";
ALTER TABLE "budgets" ADD CONSTRAINT "budgets_categoryId_familyMemberId_month_year_userId_key" UNIQUE ("category_id", "family_member_id", "month", "year", "user_id");
