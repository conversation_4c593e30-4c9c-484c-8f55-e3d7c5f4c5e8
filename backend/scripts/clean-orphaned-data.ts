import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function cleanOrphanedData() {
  try {
    console.log('🧹 Limpando dados órfãos sem userId...')

    // Execute raw SQL to delete records without userId
    const queries = [
      'DELETE FROM "goals" WHERE "user_id" IS NULL;',
      'DELETE FROM "budgets" WHERE "user_id" IS NULL;',
      'DELETE FROM "categories" WHERE "user_id" IS NULL;',
      'DELETE FROM "tags" WHERE "user_id" IS NULL;',
      'DELETE FROM "transactions" WHERE "user_id" IS NULL;'
    ]

    for (const query of queries) {
      const result = await prisma.$executeRawUnsafe(query)
      console.log(`✅ Query executada: ${query} - ${result} registros afetados`)
    }

    console.log('🎉 Limpeza concluída com sucesso!')

  } catch (error) {
    console.error('❌ Erro durante a limpeza:', error)
  } finally {
    await prisma.$disconnect()
  }
}

cleanOrphanedData()
