import { Request, Response } from 'express';
import { z } from 'zod';
import { Prisma } from '@prisma/client';
import { accountService } from '../services/account.service';
import {
  CreateAccountSchema,
  UpdateAccountSchema,
  AccountFiltersSchema,
  ArchiveAccountSchema,
  LogoUploadSchema
} from '../schemas/account.schemas';

export class AccountController {
  /**
   * Create a new account
   * @route POST /api/v1/accounts
   */
  async create(req: Request, res: Response): Promise<void> {
    try {
      // Validate request body
      const result = CreateAccountSchema.safeParse(req.body);
      if (!result.success) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Dados inválidos',
            code: 'VALIDATION_ERROR',
            details: result.error.errors.map(err => ({
              field: err.path.join('.'),
              message: err.message
            }))
          }
        });
        return;
      }

      // Create account
      const account = await accountService.create(result.data, req.user!.id);

      res.status(201).json({
        success: true,
        data: account,
        message: 'Conta criada com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get all accounts with filters and pagination
   * @route GET /api/v1/accounts
   */
  async findAll(req: Request, res: Response): Promise<void> {
    try {
      // Validate query parameters
      const result = AccountFiltersSchema.safeParse(req.query);
      if (!result.success) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Parâmetros de consulta inválidos',
            code: 'VALIDATION_ERROR',
            details: result.error.errors.map(err => ({
              field: err.path.join('.'),
              message: err.message
            }))
          }
        });
        return;
      }

      // Get accounts
      const accounts = await accountService.findAll(result.data, req.user!.id);

      res.status(200).json({
        success: true,
        data: accounts.data,
        pagination: accounts.pagination,
        message: 'Contas obtidas com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get account by ID
   * @route GET /api/v1/accounts/:id
   */
  async findById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const includeArchived = req.query.includeArchived === 'true';

      // Get account
      const account = await accountService.findById(id, includeArchived);

      if (!account) {
        res.status(404).json({
          success: false,
          error: {
            message: 'Conta não encontrada',
            code: 'ACCOUNT_NOT_FOUND'
          }
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: account,
        message: 'Conta obtida com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Update account
   * @route PUT /api/v1/accounts/:id
   */
  async update(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Validate request body
      const result = UpdateAccountSchema.safeParse(req.body);
      if (!result.success) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Dados inválidos',
            code: 'VALIDATION_ERROR',
            details: result.error.errors.map(err => ({
              field: err.path.join('.'),
              message: err.message
            }))
          }
        });
        return;
      }

      // Check if there's data to update
      if (Object.keys(result.data).length === 0) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Nenhum dado fornecido para atualização',
            code: 'NO_UPDATE_DATA'
          }
        });
        return;
      }

      // Update account
      const account = await accountService.update(id, result.data);

      res.status(200).json({
        success: true,
        data: account,
        message: 'Conta atualizada com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Archive/Unarchive account
   * @route PATCH /api/v1/accounts/:id/archive
   */
  async archive(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Validate request body
      const result = ArchiveAccountSchema.safeParse(req.body);
      if (!result.success) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Dados inválidos',
            code: 'VALIDATION_ERROR',
            details: result.error.errors.map(err => ({
              field: err.path.join('.'),
              message: err.message
            }))
          }
        });
        return;
      }

      // Archive/Unarchive account
      const account = await accountService.archive(id, result.data.archived);

      const action = result.data.archived ? 'arquivada' : 'reativada';
      res.status(200).json({
        success: true,
        data: account,
        message: `Conta ${action} com sucesso`
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Permanently delete account
   * @route DELETE /api/v1/accounts/:id
   */
  async delete(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Delete account
      await accountService.delete(id);

      res.status(200).json({
        success: true,
        message: 'Conta deletada permanentemente com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Upload account logo
   * @route POST /api/v1/accounts/:id/logo
   */
  async uploadLogo(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Validate file upload
      if (!req.file) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Nenhum arquivo foi enviado',
            code: 'NO_FILE_UPLOADED'
          }
        });
        return;
      }

      const result = LogoUploadSchema.safeParse({ file: req.file });
      if (!result.success) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Arquivo inválido',
            code: 'INVALID_FILE',
            details: result.error.errors.map(err => ({
              field: err.path.join('.'),
              message: err.message
            }))
          }
        });
        return;
      }

      // Update account with logo path
      const logoPath = `/icons/brands/${req.file.filename}`;
      const account = await accountService.update(id, { logoPath });

      res.status(200).json({
        success: true,
        data: account,
        message: 'Logo da conta atualizado com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Handle errors and send appropriate response
   */
  private handleError(error: unknown, res: Response): void {
    // Handle Zod validation errors
    if (error instanceof z.ZodError) {
      res.status(400).json({
        success: false,
        error: {
          message: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        }
      });
      return;
    }

    // Handle Prisma errors
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2002':
          res.status(409).json({
            success: false,
            error: {
              message: 'Violação de restrição única',
              code: 'UNIQUE_CONSTRAINT_VIOLATION',
              details: error.meta
            }
          });
          return;
        case 'P2003':
          res.status(400).json({
            success: false,
            error: {
              message: 'Violação de chave estrangeira',
              code: 'FOREIGN_KEY_CONSTRAINT_VIOLATION',
              details: error.meta
            }
          });
          return;
        case 'P2025':
          res.status(404).json({
            success: false,
            error: {
              message: 'Registro não encontrado',
              code: 'RECORD_NOT_FOUND'
            }
          });
          return;
      }
    }

    // Handle application errors
    if (error instanceof Error) {
      // Check for specific error types
      if (error.message.includes('não encontrado') || error.message.includes('não encontrada')) {
        res.status(404).json({
          success: false,
          error: {
            message: error.message,
            code: 'ACCOUNT_NOT_FOUND'
          }
        });
        return;
      }

      if (error.message.includes('já existe') || error.message.includes('já está')) {
        res.status(409).json({
          success: false,
          error: {
            message: error.message,
            code: 'CONFLICT'
          }
        });
        return;
      }

      if (error.message.includes('não é possível deletar')) {
        res.status(400).json({
          success: false,
          error: {
            message: error.message,
            code: 'DELETE_CONSTRAINT_VIOLATION'
          }
        });
        return;
      }

      res.status(400).json({
        success: false,
        error: {
          message: error.message,
          code: 'ACCOUNT_ERROR'
        }
      });
      return;
    }

    // Generic server error
    res.status(500).json({
      success: false,
      error: {
        message: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      }
    });
  }
}

export const accountController = new AccountController();
