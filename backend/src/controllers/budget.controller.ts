import { Request, Response } from 'express';
import { z } from 'zod';
import { budgetService } from '../services/budget.service';
import {
  CreateBudgetSchema,
  UpdateBudgetSchema,
  BudgetFiltersSchema,
  BudgetProgressFiltersSchema
} from '../schemas/budget.schemas';

export class BudgetController {
  /**
   * Create a new budget
   * @route POST /api/v1/budgets
   */
  async create(req: Request, res: Response): Promise<void> {
    try {
      // Validate request body
      const validatedData = CreateBudgetSchema.parse(req.body);

      // Create budget
      const budget = await budgetService.create(validatedData, req.user!.id);

      res.status(201).json({
        success: true,
        data: budget,
        message: 'Orçamento criado com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get all budgets with filters and pagination
   * @route GET /api/v1/budgets
   */
  async findAll(req: Request, res: Response): Promise<void> {
    try {
      // Validate query parameters
      const validatedFilters = BudgetFiltersSchema.parse({
        ...req.query,
        // Convert string numbers to actual numbers
        ...(req.query.month && { month: parseInt(req.query.month as string) }),
        ...(req.query.year && { year: parseInt(req.query.year as string) }),
        ...(req.query.page && { page: parseInt(req.query.page as string) }),
        ...(req.query.limit && { limit: parseInt(req.query.limit as string) }),
        ...(req.query.includeProgress && { includeProgress: req.query.includeProgress === 'true' })
      });

      // Get budgets
      const result = await budgetService.findAll(validatedFilters, req.user!.id);

      res.status(200).json({
        success: true,
        data: result.data,
        pagination: result.pagination,
        message: 'Orçamentos recuperados com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get budget by ID
   * @route GET /api/v1/budgets/:id
   */
  async findById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const includeProgress = req.query.includeProgress === 'true';

      // Validate ID format
      if (!id || typeof id !== 'string') {
        res.status(400).json({
          success: false,
          error: {
            message: 'ID do orçamento é obrigatório',
            code: 'INVALID_ID'
          }
        });
        return;
      }

      // Get budget
      const budget = await budgetService.findById(id, includeProgress);

      if (!budget) {
        res.status(404).json({
          success: false,
          error: {
            message: 'Orçamento não encontrado',
            code: 'BUDGET_NOT_FOUND'
          }
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: budget,
        message: 'Orçamento recuperado com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Update budget
   * @route PUT /api/v1/budgets/:id
   */
  async update(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Validate ID format
      if (!id || typeof id !== 'string') {
        res.status(400).json({
          success: false,
          error: {
            message: 'ID do orçamento é obrigatório',
            code: 'INVALID_ID'
          }
        });
        return;
      }

      // Validate request body
      const validatedData = UpdateBudgetSchema.parse(req.body);

      // Check if there's data to update
      if (Object.keys(validatedData).length === 0) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Nenhum dado fornecido para atualização',
            code: 'NO_UPDATE_DATA'
          }
        });
        return;
      }

      // Update budget
      const budget = await budgetService.update(id, validatedData);

      res.status(200).json({
        success: true,
        data: budget,
        message: 'Orçamento atualizado com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Delete budget
   * @route DELETE /api/v1/budgets/:id
   */
  async delete(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Validate ID format
      if (!id || typeof id !== 'string') {
        res.status(400).json({
          success: false,
          error: {
            message: 'ID do orçamento é obrigatório',
            code: 'INVALID_ID'
          }
        });
        return;
      }

      // Delete budget
      await budgetService.delete(id);

      res.status(200).json({
        success: true,
        message: 'Orçamento deletado com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get budget progress for a specific budget
   * @route GET /api/v1/budgets/:id/progress
   */
  async getBudgetProgress(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Validate ID format
      if (!id || typeof id !== 'string') {
        res.status(400).json({
          success: false,
          error: {
            message: 'ID do orçamento é obrigatório',
            code: 'INVALID_ID'
          }
        });
        return;
      }

      // Get budget with progress
      const budget = await budgetService.findById(id, true);

      if (!budget) {
        res.status(404).json({
          success: false,
          error: {
            message: 'Orçamento não encontrado',
            code: 'BUDGET_NOT_FOUND'
          }
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: {
          budgetId: budget.id,
          progress: budget.progress
        },
        message: 'Progresso do orçamento recuperado com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get budget summary and report
   * @route GET /api/v1/budgets/report
   */
  async getBudgetReport(req: Request, res: Response): Promise<void> {
    try {
      // Validate query parameters
      const validatedFilters = BudgetProgressFiltersSchema.parse({
        ...req.query,
        // Convert string numbers to actual numbers
        ...(req.query.month && { month: parseInt(req.query.month as string) }),
        ...(req.query.year && { year: parseInt(req.query.year as string) }),
        ...(req.query.startMonth && { startMonth: parseInt(req.query.startMonth as string) }),
        ...(req.query.startYear && { startYear: parseInt(req.query.startYear as string) }),
        ...(req.query.endMonth && { endMonth: parseInt(req.query.endMonth as string) }),
        ...(req.query.endYear && { endYear: parseInt(req.query.endYear as string) }),
        ...(req.query.alertThreshold && { alertThreshold: parseFloat(req.query.alertThreshold as string) }),
        ...(req.query.includeSubcategories && { includeSubcategories: req.query.includeSubcategories === 'true' })
      });

      // Get budget report
      const report = await budgetService.getBudgetReport(validatedFilters);

      res.status(200).json({
        success: true,
        data: report,
        message: 'Relatório de orçamentos gerado com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Handle errors and send appropriate response
   */
  private handleError(error: unknown, res: Response): void {
    if (error instanceof z.ZodError) {
      res.status(400).json({
        success: false,
        error: {
          message: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        }
      });
      return;
    }

    if (error instanceof Error) {
      // Check for specific business logic errors
      if (error.message.includes('não encontrado') || error.message.includes('não encontrada')) {
        res.status(404).json({
          success: false,
          error: {
            message: error.message,
            code: 'NOT_FOUND'
          }
        });
        return;
      }

      if (error.message.includes('já existe') || error.message.includes('Já existe')) {
        res.status(409).json({
          success: false,
          error: {
            message: error.message,
            code: 'CONFLICT'
          }
        });
        return;
      }

      if (error.message.includes('arquivado') || error.message.includes('arquivada')) {
        res.status(400).json({
          success: false,
          error: {
            message: error.message,
            code: 'ARCHIVED_RESOURCE'
          }
        });
        return;
      }

      // Generic business logic error
      res.status(400).json({
        success: false,
        error: {
          message: error.message,
          code: 'BUSINESS_LOGIC_ERROR'
        }
      });
      return;
    }

    // Generic server error
    console.error('Unexpected error in BudgetController:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Erro interno do servidor',
        code: 'INTERNAL_SERVER_ERROR'
      }
    });
  }
}

export const budgetController = new BudgetController();
