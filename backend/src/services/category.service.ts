import { Prisma } from '@prisma/client';
import prisma from '../lib/prisma';
import {
  CreateCategoryRequest,
  UpdateCategoryRequest,
  CategoryFilters,
  CategoryResponse,
  PaginatedCategoriesResponse,
  CategoryTreeResponse,
  CategoryHierarchyValidation
} from '../schemas/category.schemas';

export class CategoryService {
  /**
   * Create a new category with hierarchy validation
   */
  async create(data: CreateCategoryRequest, userId: string): Promise<CategoryResponse> {
    // Check if name already exists within the same parent for this user (case-insensitive)
    const existingCategory = await prisma.category.findFirst({
      where: {
        name: {
          equals: data.name,
          mode: 'insensitive'
        },
        parentId: data.parentId || null,
        userId,
        deletedAt: null
      }
    });

    if (existingCategory) {
      const scope = data.parentId ? 'na mesma categoria pai' : 'no nível principal';
      throw new Error(`Já existe uma categoria com este nome ${scope}`);
    }

    // Validate hierarchy if parentId is provided
    if (data.parentId) {
      await this.validateHierarchy(data.parentId, null, userId);
    }

    const category = await prisma.category.create({
      data: {
        name: data.name,
        color: data.color,
        parentId: data.parentId,
        userId
      },
      include: {
        parent: {
          select: {
            id: true,
            name: true,
            color: true
          }
        },
        children: {
          select: {
            id: true,
            name: true,
            color: true
          },
          where: { deletedAt: null }
        }
      }
    });

    return this.formatCategory(category);
  }

  /**
   * Get all categories with filters and pagination
   */
  async findAll(filters: CategoryFilters = {}, userId: string): Promise<PaginatedCategoriesResponse> {
    const {
      name,
      parentId,
      includeChildren = false,
      onlyParents = false,
      onlyChildren = false,
      includeArchived = false,
      includeDeleted = false,
      page = 1,
      limit = 20
    } = filters;

    // Build where clause
    const where: Prisma.CategoryWhereInput = {
      userId // Filter by user
    };

    if (name) {
      where.name = {
        contains: name,
        mode: 'insensitive'
      };
    }

    if (parentId !== undefined) {
      where.parentId = parentId;
    }

    if (onlyParents) {
      where.parentId = null;
    }

    if (onlyChildren) {
      where.parentId = { not: null };
    }

    // Handle archived/deleted filtering
    if (!includeArchived && !includeDeleted) {
      // Show only active categories (not archived and not deleted)
      where.deletedAt = null;
    } else if (!includeDeleted && includeArchived) {
      // Show both active and archived categories, but not permanently deleted
      // In our system, archived = soft deleted, so we include all
      // This is handled by not adding any deletedAt filter
    } else if (includeDeleted && !includeArchived) {
      // Show only permanently deleted categories
      where.deletedAt = { not: null };
    }
    // If both includeArchived and includeDeleted are true, show all categories

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get total count
    const total = await prisma.category.count({ where });

    // Get categories with relationships
    const categories = await prisma.category.findMany({
      where,
      include: {
        parent: {
          select: {
            id: true,
            name: true,
            color: true
          }
        },
        children: includeChildren ? {
          select: {
            id: true,
            name: true,
            color: true
          },
          where: { deletedAt: null }
        } : false,
        _count: {
          select: {
            transactions: true,
            budgets: true
          }
        }
      },
      orderBy: [
        { deletedAt: 'asc' }, // Non-archived first
        { parentId: 'asc' }, // Parent categories first
        { name: 'asc' }
      ],
      skip,
      take: limit
    });

    return {
      data: categories.map(category => this.formatCategory(category)),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * Get category by ID
   */
  async findById(id: string, userId: string, includeArchived = false): Promise<CategoryResponse | null> {
    const where: Prisma.CategoryWhereInput = {
      id,
      userId // Filter by user
    };

    if (!includeArchived) {
      where.deletedAt = null;
    }

    const category = await prisma.category.findFirst({
      where,
      include: {
        parent: {
          select: {
            id: true,
            name: true,
            color: true
          }
        },
        children: {
          select: {
            id: true,
            name: true,
            color: true
          },
          where: { deletedAt: null }
        },
        _count: {
          select: {
            transactions: true,
            budgets: true
          }
        }
      }
    });

    if (!category) {
      return null;
    }

    return this.formatCategory(category);
  }

  /**
   * Get categories for selection dropdown
   */
  async getForSelection(userId: string, onlyActive = false, excludeId?: string): Promise<Array<{
    value: string;
    label: string;
    level: number;
    disabled?: boolean;
  }>> {
    const where: Prisma.CategoryWhereInput = {
      userId // Filter by user
    };

    if (onlyActive) {
      where.deletedAt = null;
    }

    if (excludeId) {
      where.id = { not: excludeId };
    }

    // Get all categories ordered by hierarchy
    const categories = await prisma.category.findMany({
      where,
      include: {
        parent: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: [
        { parentId: 'asc' }, // Parent categories first
        { name: 'asc' }
      ]
    });

    // Build hierarchical list
    const result: Array<{
      value: string;
      label: string;
      level: number;
      disabled?: boolean;
    }> = [];

    // First add parent categories
    const parentCategories = categories.filter(cat => !cat.parentId);
    for (const parent of parentCategories) {
      result.push({
        value: parent.id,
        label: parent.name,
        level: 0,
        disabled: false
      });

      // Then add children of this parent
      const children = categories.filter(cat => cat.parentId === parent.id);
      for (const child of children) {
        result.push({
          value: child.id,
          label: child.name,
          level: 1,
          disabled: false
        });
      }
    }

    return result;
  }

  /**
   * Get category tree for hierarchical display
   */
  async getCategoryTree(userId: string, includeArchived = false): Promise<CategoryTreeResponse[]> {
    const where: Prisma.CategoryWhereInput = {
      userId, // Filter by user
      parentId: null // Only root categories
    };

    if (!includeArchived) {
      where.deletedAt = null;
    }

    const rootCategories = await prisma.category.findMany({
      where,
      include: {
        children: {
          where: includeArchived ? {} : { deletedAt: null },
          include: {
            _count: {
              select: {
                transactions: true,
                budgets: true
              }
            }
          },
          orderBy: { name: 'asc' }
        },
        _count: {
          select: {
            transactions: true,
            budgets: true
          }
        }
      },
      orderBy: { name: 'asc' }
    });

    return rootCategories.map(category => this.formatCategoryTree(category));
  }

  /**
   * Update category with hierarchy validation
   */
  async update(id: string, data: UpdateCategoryRequest, userId: string): Promise<CategoryResponse> {
    // Check if category exists and is not deleted
    const existingCategory = await prisma.category.findFirst({
      where: {
        id,
        userId, // Ensure user owns this category
        deletedAt: null
      }
    });

    if (!existingCategory) {
      throw new Error('Categoria não encontrada');
    }

    // Check name uniqueness if name is being updated
    if (data.name && data.name !== existingCategory.name) {
      const nameExists = await prisma.category.findFirst({
        where: {
          name: {
            equals: data.name,
            mode: 'insensitive'
          },
          parentId: data.parentId !== undefined ? data.parentId : existingCategory.parentId,
          deletedAt: null,
          id: { not: id }
        }
      });

      if (nameExists) {
        const scope = (data.parentId !== undefined ? data.parentId : existingCategory.parentId) 
          ? 'na mesma categoria pai' : 'no nível principal';
        throw new Error(`Já existe uma categoria com este nome ${scope}`);
      }
    }

    // Validate hierarchy if parentId is being changed
    if (data.parentId !== undefined && data.parentId !== existingCategory.parentId) {
      await this.validateHierarchy(data.parentId, id);
    }

    const updatedCategory = await prisma.category.update({
      where: { id },
      data: {
        name: data.name,
        color: data.color,
        parentId: data.parentId,
        version: { increment: 1 }
      },
      include: {
        parent: {
          select: {
            id: true,
            name: true,
            color: true
          }
        },
        children: {
          select: {
            id: true,
            name: true,
            color: true
          },
          where: { deletedAt: null }
        },
        _count: {
          select: {
            transactions: true,
            budgets: true
          }
        }
      }
    });

    return this.formatCategory(updatedCategory);
  }

  /**
   * Archive/Unarchive category (soft delete)
   */
  async archive(id: string, archived: boolean): Promise<CategoryResponse> {
    const existingCategory = await prisma.category.findUnique({
      where: { id },
      include: {
        children: {
          where: { deletedAt: null }
        }
      }
    });

    if (!existingCategory) {
      throw new Error('Categoria não encontrada');
    }

    // Check if category is already in the desired state
    const isCurrentlyArchived = existingCategory.deletedAt !== null;
    if (isCurrentlyArchived === archived) {
      const action = archived ? 'arquivada' : 'ativa';
      throw new Error(`Categoria já está ${action}`);
    }

    // If archiving a parent category, archive all children too
    if (archived && existingCategory.children.length > 0) {
      await prisma.$transaction(async (tx) => {
        // Archive all children first
        await tx.category.updateMany({
          where: {
            parentId: id,
            deletedAt: null
          },
          data: {
            deletedAt: new Date(),
            version: { increment: 1 }
          }
        });

        // Archive parent category
        await tx.category.update({
          where: { id },
          data: {
            deletedAt: new Date(),
            version: { increment: 1 }
          }
        });
      });
    } else {
      await prisma.category.update({
        where: { id },
        data: {
          deletedAt: archived ? new Date() : null,
          version: { increment: 1 }
        }
      });
    }

    // Return updated category
    const updatedCategory = await prisma.category.findUnique({
      where: { id },
      include: {
        parent: {
          select: {
            id: true,
            name: true,
            color: true
          }
        },
        children: {
          select: {
            id: true,
            name: true,
            color: true
          },
          where: { deletedAt: null }
        },
        _count: {
          select: {
            transactions: true,
            budgets: true
          }
        }
      }
    });

    return this.formatCategory(updatedCategory!);
  }

  /**
   * Get category statistics
   */
  async getStats(userId: string): Promise<{
    totalCategories: number;
    parentCategories: number;
    subcategories: number;
    archivedCategories: number;
    categoriesWithTransactions: number;
    categoriesWithBudgets: number;
  }> {
    // Get total categories (excluding deleted)
    const totalCategories = await prisma.category.count({
      where: {
        userId,
        deletedAt: null
      }
    });

    // Get parent categories (no parent, not deleted)
    const parentCategories = await prisma.category.count({
      where: {
        userId,
        parentId: null,
        deletedAt: null
      }
    });

    // Get subcategories (has parent, not deleted)
    const subcategories = await prisma.category.count({
      where: {
        userId,
        parentId: { not: null },
        deletedAt: null
      }
    });

    // Get archived categories
    const archivedCategories = await prisma.category.count({
      where: {
        userId,
        deletedAt: { not: null }
      }
    });

    // Get categories with transactions
    const categoriesWithTransactions = await prisma.category.count({
      where: {
        userId,
        deletedAt: null,
        transactions: {
          some: {}
        }
      }
    });

    // Get categories with budgets
    const categoriesWithBudgets = await prisma.category.count({
      where: {
        userId,
        deletedAt: null,
        budgets: {
          some: {}
        }
      }
    });

    return {
      totalCategories,
      parentCategories,
      subcategories,
      archivedCategories,
      categoriesWithTransactions,
      categoriesWithBudgets
    };
  }

  /**
   * Permanently delete category
   */
  async delete(id: string): Promise<void> {
    const existingCategory = await prisma.category.findUnique({
      where: { id },
      include: {
        children: true
      }
    });

    if (!existingCategory) {
      throw new Error('Categoria não encontrada');
    }

    // Use transaction to ensure data integrity
    await prisma.$transaction(async (tx) => {
      // Check if category has any relationships that would prevent deletion
      const hasTransactions = await tx.transaction.findFirst({
        where: { categoryId: id }
      });

      const hasBudgets = await tx.budget.findFirst({
        where: { categoryId: id }
      });

      const hasRecurringTransactions = await tx.recurringTransaction.findFirst({
        where: { categoryId: id }
      });

      if (hasTransactions || hasBudgets || hasRecurringTransactions) {
        throw new Error('Não é possível deletar categoria que possui transações, orçamentos ou transações recorrentes associadas');
      }

      // If it's a parent category, check children
      if (existingCategory.children.length > 0) {
        for (const child of existingCategory.children) {
          const childHasTransactions = await tx.transaction.findFirst({
            where: { categoryId: child.id }
          });

          const childHasBudgets = await tx.budget.findFirst({
            where: { categoryId: child.id }
          });

          const childHasRecurringTransactions = await tx.recurringTransaction.findFirst({
            where: { categoryId: child.id }
          });

          if (childHasTransactions || childHasBudgets || childHasRecurringTransactions) {
            throw new Error('Não é possível deletar categoria pai que possui subcategorias com transações, orçamentos ou transações recorrentes associadas');
          }
        }

        // Delete all children first
        await tx.category.deleteMany({
          where: { parentId: id }
        });
      }

      // Delete the category
      await tx.category.delete({
        where: { id }
      });
    });
  }

  /**
   * Validate category hierarchy (max 2 levels, no circular references)
   */
  private async validateHierarchy(parentId: string | null, categoryId: string | null, userId: string): Promise<CategoryHierarchyValidation> {
    const validation: CategoryHierarchyValidation = {
      isValid: true,
      errors: [],
      maxDepthReached: false,
      circularReference: false
    };

    if (!parentId) {
      return validation; // No parent, always valid
    }

    // Check if parent exists and is not deleted
    const parent = await prisma.category.findFirst({
      where: {
        id: parentId,
        userId, // Ensure parent belongs to the same user
        deletedAt: null
      },
      include: {
        parent: true
      }
    });

    if (!parent) {
      validation.isValid = false;
      validation.errors.push('Categoria pai não encontrada');
      return validation;
    }

    // Check for circular reference
    if (categoryId && parentId === categoryId) {
      validation.isValid = false;
      validation.circularReference = true;
      validation.errors.push('Uma categoria não pode ser pai de si mesma');
      return validation;
    }

    // Check if parent already has a parent (max 2 levels)
    if (parent.parent) {
      validation.isValid = false;
      validation.maxDepthReached = true;
      validation.errors.push('Hierarquia máxima de 2 níveis atingida. Subcategorias não podem ter filhas');
      return validation;
    }

    // If updating existing category, check for circular reference in the hierarchy
    if (categoryId) {
      const descendants = await this.getCategoryDescendants(categoryId);
      if (descendants.includes(parentId)) {
        validation.isValid = false;
        validation.circularReference = true;
        validation.errors.push('Referência circular detectada na hierarquia');
        return validation;
      }
    }

    if (!validation.isValid) {
      throw new Error(validation.errors.join('; '));
    }

    return validation;
  }

  /**
   * Get all descendant category IDs
   */
  private async getCategoryDescendants(categoryId: string): Promise<string[]> {
    const descendants: string[] = [];

    const children = await prisma.category.findMany({
      where: {
        parentId: categoryId,
        deletedAt: null
      },
      select: { id: true }
    });

    for (const child of children) {
      descendants.push(child.id);
      // Recursively get descendants (though we limit to 2 levels)
      const childDescendants = await this.getCategoryDescendants(child.id);
      descendants.push(...childDescendants);
    }

    return descendants;
  }

  /**
   * Format category for response
   */
  private formatCategory(category: any): CategoryResponse {
    return {
      id: category.id,
      name: category.name,
      color: category.color,
      parentId: category.parentId,
      archived: category.deletedAt !== null,
      createdAt: category.createdAt.toISOString(),
      updatedAt: category.updatedAt.toISOString(),
      version: category.version,
      level: category.parentId ? 1 : 0,
      hasChildren: category.children ? category.children.length > 0 : false,
      parent: category.parent ? {
        id: category.parent.id,
        name: category.parent.name,
        color: category.parent.color
      } : undefined,
      children: category.children ? category.children.map((child: any) => ({
        id: child.id,
        name: child.name,
        color: child.color
      })) : undefined,
      transactionCount: category._count?.transactions || 0,
      budgetCount: category._count?.budgets || 0
    };
  }

  /**
   * Format category tree for hierarchical response
   */
  private formatCategoryTree(category: any): CategoryTreeResponse {
    return {
      id: category.id,
      name: category.name,
      color: category.color,
      archived: category.deletedAt !== null,
      level: 0,
      transactionCount: category._count?.transactions || 0,
      budgetCount: category._count?.budgets || 0,
      children: category.children ? category.children.map((child: any) => ({
        id: child.id,
        name: child.name,
        color: child.color,
        archived: child.deletedAt !== null,
        level: 1,
        transactionCount: child._count?.transactions || 0,
        budgetCount: child._count?.budgets || 0,
        children: [] // Max 2 levels
      })) : []
    };
  }
}

export const categoryService = new CategoryService();
