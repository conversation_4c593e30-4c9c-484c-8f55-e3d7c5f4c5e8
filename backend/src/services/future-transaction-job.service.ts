import prisma from '../lib/prisma';
import { TransactionService } from './transaction.service';
import { TransactionType } from '@prisma/client';

/**
 * Job execution statistics
 */
interface JobExecutionStats {
  processedTransactions: number;
  failedTransactions: number;
  skippedTransactions: number;
  totalProcessingTime: number;
  errors: Array<{
    transactionId: string;
    error: string;
    timestamp: Date;
  }>;
}

/**
 * Service for processing future transactions that have reached their execution date
 */
export class FutureTransactionJobService {
  private transactionService: TransactionService;

  constructor() {
    this.transactionService = new TransactionService();
  }

  /**
   * Initialize the service (for compatibility)
   */
  initialize(): void {
    // Service is ready to use
  }

  /**
   * Main job execution method - processes all future transactions due today
   */
  async execute(targetDate?: Date): Promise<JobExecutionStats> {
    const startTime = Date.now();
    const executionDate = targetDate || new Date();
    
    console.log(`[FutureTransactionJob] Starting execution for date: ${executionDate.toISOString()}`);

    const stats: JobExecutionStats = {
      processedTransactions: 0,
      failedTransactions: 0,
      skippedTransactions: 0,
      totalProcessingTime: 0,
      errors: []
    };

    try {
      // Get all future transactions that should be processed today
      const futureTransactions = await this.getFutureTransactionsForToday(executionDate);
      
      console.log(`[FutureTransactionJob] Found ${futureTransactions.length} future transactions to process`);

      // Process each transaction
      for (const transaction of futureTransactions) {
        try {
          await this.processFutureTransaction(transaction, stats);
        } catch (error) {
          console.error(`[FutureTransactionJob] Failed to process transaction ${transaction.id}:`, error);
          stats.failedTransactions++;
          stats.errors.push({
            transactionId: transaction.id,
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date()
          });
        }
      }

      stats.totalProcessingTime = Date.now() - startTime;

      console.log(`[FutureTransactionJob] Execution completed:`, {
        processed: stats.processedTransactions,
        failed: stats.failedTransactions,
        skipped: stats.skippedTransactions,
        totalTime: `${stats.totalProcessingTime}ms`
      });

      return stats;

    } catch (error) {
      console.error('[FutureTransactionJob] Job execution failed:', error);
      stats.totalProcessingTime = Date.now() - startTime;
      throw error;
    }
  }

  /**
   * Get future transactions that should be processed today
   */
  private async getFutureTransactionsForToday(targetDate: Date) {
    const startOfDay = new Date(targetDate);
    startOfDay.setHours(0, 0, 0, 0);

    const endOfDay = new Date(targetDate);
    endOfDay.setHours(23, 59, 59, 999);

    return await prisma.transaction.findMany({
      where: {
        isFuture: true,
        deletedAt: null,
        transactionDate: {
          gte: startOfDay,
          lte: endOfDay
        }
      },
      include: {
        account: {
          select: {
            id: true,
            name: true,
            type: true,
            currency: true,
            currentBalance: true
          }
        },
        destinationAccount: {
          select: {
            id: true,
            name: true,
            type: true,
            currency: true,
            currentBalance: true
          }
        },
        category: {
          select: {
            id: true,
            name: true
          }
        },
        members: {
          include: {
            familyMember: {
              select: {
                id: true,
                name: true
              }
            }
          }
        }
      },
      orderBy: {
        transactionDate: 'asc'
      }
    });
  }

  /**
   * Process a single future transaction
   */
  private async processFutureTransaction(
    transaction: any,
    stats: JobExecutionStats
  ): Promise<void> {
    console.log(`[FutureTransactionJob] Processing future transaction: ${transaction.description}`);

    // Validate that the transaction should be processed
    if (!this.shouldProcessTransaction(transaction)) {
      console.log(`[FutureTransactionJob] Skipping transaction ${transaction.id} - validation failed`);
      stats.skippedTransactions++;
      return;
    }

    // Use Prisma transaction for atomicity
    await prisma.$transaction(async (tx) => {
      // Update the transaction to mark it as no longer future
      await tx.transaction.update({
        where: { id: transaction.id },
        data: { isFuture: false }
      });

      // Update account balances
      await this.updateAccountBalances(tx, transaction);

      console.log(`[FutureTransactionJob] Successfully processed transaction ${transaction.id}`);
      stats.processedTransactions++;
    });
  }

  /**
   * Validate if a transaction should be processed
   */
  private shouldProcessTransaction(transaction: any): boolean {
    // Check if account still exists and is active
    if (!transaction.account || transaction.account.deletedAt) {
      console.warn(`[FutureTransactionJob] Account ${transaction.accountId} no longer exists or is deleted`);
      return false;
    }

    // For transfers, check destination account
    if (transaction.type === TransactionType.TRANSFER) {
      if (!transaction.destinationAccount || transaction.destinationAccount.deletedAt) {
        console.warn(`[FutureTransactionJob] Destination account ${transaction.destinationAccountId} no longer exists or is deleted`);
        return false;
      }
    }

    // Check for sufficient balance for expenses and transfers
    if (transaction.type === TransactionType.EXPENSE || transaction.type === TransactionType.TRANSFER) {
      const currentBalance = Number(transaction.account.currentBalance);
      const transactionAmount = Number(transaction.amount);

      if (currentBalance < transactionAmount) {
        console.warn(`[FutureTransactionJob] Insufficient balance for transaction ${transaction.id}. Balance: ${currentBalance}, Required: ${transactionAmount}`);
        // We still process it but log the warning - business decision
      }
    }

    return true;
  }

  /**
   * Update account balances for the processed transaction
   */
  private async updateAccountBalances(tx: any, transaction: any): Promise<void> {
    const amount = Number(transaction.amount);

    // Update source account balance
    await this.updateAccountBalance(tx, transaction.accountId, transaction.type, amount);

    // Update destination account balance for transfers
    if (transaction.type === TransactionType.TRANSFER && transaction.destinationAccountId) {
      const destinationAmount = transaction.destinationAmount 
        ? Number(transaction.destinationAmount) 
        : amount;
      
      await this.updateAccountBalance(
        tx, 
        transaction.destinationAccountId, 
        TransactionType.INCOME, 
        destinationAmount
      );
    }
  }

  /**
   * Update account balance based on transaction type
   */
  private async updateAccountBalance(
    tx: any,
    accountId: string,
    transactionType: TransactionType,
    amount: number
  ): Promise<void> {
    const account = await tx.account.findUnique({
      where: { id: accountId },
      select: { currentBalance: true }
    });

    if (!account) {
      throw new Error(`Account ${accountId} not found`);
    }

    let balanceChange = 0;

    switch (transactionType) {
      case TransactionType.INCOME:
        balanceChange = amount;
        break;
      case TransactionType.EXPENSE:
        balanceChange = -amount;
        break;
      case TransactionType.TRANSFER:
        balanceChange = -amount; // Source account loses money
        break;
    }

    const currentBalance = Number(account.currentBalance);
    const newBalance = currentBalance + balanceChange;

    await tx.account.update({
      where: { id: accountId },
      data: { currentBalance: newBalance }
    });

    console.log(`[FutureTransactionJob] Updated account ${accountId} balance: ${currentBalance} → ${newBalance} (${balanceChange > 0 ? '+' : ''}${balanceChange})`);
  }

  /**
   * Get statistics about pending future transactions
   */
  async getPendingFutureTransactionsStats(userId: string): Promise<{
    totalPending: number;
    dueToday: number;
    dueThisWeek: number;
    dueThisMonth: number;
    overdue: number;
  }> {
    const now = new Date();
    const today = new Date(now);
    today.setHours(23, 59, 59, 999);

    const oneWeekFromNow = new Date(now);
    oneWeekFromNow.setDate(oneWeekFromNow.getDate() + 7);

    const oneMonthFromNow = new Date(now);
    oneMonthFromNow.setMonth(oneMonthFromNow.getMonth() + 1);

    const [totalPending, dueToday, dueThisWeek, dueThisMonth, overdue] = await Promise.all([
      // Total pending
      prisma.transaction.count({
        where: {
          userId,
          isFuture: true,
          deletedAt: null
        }
      }),

      // Due today
      prisma.transaction.count({
        where: {
          userId,
          isFuture: true,
          deletedAt: null,
          transactionDate: {
            lte: today
          }
        }
      }),

      // Due this week
      prisma.transaction.count({
        where: {
          userId,
          isFuture: true,
          deletedAt: null,
          transactionDate: {
            lte: oneWeekFromNow
          }
        }
      }),

      // Due this month
      prisma.transaction.count({
        where: {
          userId,
          isFuture: true,
          deletedAt: null,
          transactionDate: {
            lte: oneMonthFromNow
          }
        }
      }),

      // Overdue
      prisma.transaction.count({
        where: {
          userId,
          isFuture: true,
          deletedAt: null,
          transactionDate: {
            lt: now
          }
        }
      })
    ]);

    return {
      totalPending,
      dueToday,
      dueThisWeek,
      dueThisMonth,
      overdue
    };
  }
}

// Export singleton instance
export const futureTransactionJobService = new FutureTransactionJobService();
