import { Prisma } from '@prisma/client';
import prisma from '../lib/prisma';
import {
  CreateTagData,
  UpdateTagData,
  TagFilters,
  TagResponse,
  TagListResponse
} from '../schemas/tag.schemas';

export class TagService {
  /**
   * Create a new tag with unique name validation
   */
  async create(data: CreateTagData, userId: string): Promise<TagResponse> {
    // Check if name already exists for this user (case-insensitive)
    const existingTag = await prisma.tag.findFirst({
      where: {
        name: {
          equals: data.name,
          mode: 'insensitive'
        },
        userId,
        deletedAt: null
      }
    });

    if (existingTag) {
      throw new Error('Já existe uma tag com este nome');
    }

    const tag = await prisma.tag.create({
      data: {
        name: data.name,
        color: data.color,
        userId
      },
      include: {
        _count: {
          select: {
            transactionTags: true
          }
        }
      }
    });

    return this.formatTag(tag);
  }

  /**
   * Get all tags with filters and pagination
   */
  async findAll(filters: Partial<TagFilters> = {}, userId: string): Promise<TagListResponse> {
    const {
      search,
      includeArchived = false,
      page = 1,
      limit = 20,
      sortBy = 'name',
      sortOrder = 'asc'
    } = filters;

    // Build where clause
    const where: Prisma.TagWhereInput = {
      userId // Filter by user
    };

    if (search) {
      where.name = {
        contains: search,
        mode: 'insensitive'
      };
    }

    if (!includeArchived) {
      where.deletedAt = null;
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Build order by clause
    let orderBy: any = [];

    if (sortBy === 'usageCount') {
      orderBy.push({
        transactionTags: {
          _count: sortOrder
        }
      });
    } else if (sortBy === 'createdAt') {
      orderBy.push({ createdAt: sortOrder });
    } else {
      orderBy.push({ name: sortOrder });
    }

    // Always put non-archived first if including archived
    if (includeArchived) {
      orderBy.unshift({ deletedAt: 'asc' });
    }

    // Get total count
    const total = await prisma.tag.count({ where });

    // Get tags with usage count
    const tags = await prisma.tag.findMany({
      where,
      include: {
        _count: {
          select: {
            transactionTags: true
          }
        }
      },
      orderBy,
      skip,
      take: limit
    });

    return {
      data: tags.map(tag => this.formatTag(tag)),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * Get tag by ID
   */
  async findById(id: string, userId: string, includeArchived = false): Promise<TagResponse | null> {
    const where: Prisma.TagWhereInput = {
      id,
      userId // Filter by user
    };

    if (!includeArchived) {
      where.deletedAt = null;
    }

    const tag = await prisma.tag.findFirst({
      where,
      include: {
        _count: {
          select: {
            transactionTags: true
          }
        }
      }
    });

    if (!tag) {
      return null;
    }

    return this.formatTag(tag);
  }

  /**
   * Update tag with unique name validation
   */
  async update(id: string, data: UpdateTagData, userId: string): Promise<TagResponse> {
    // Check if tag exists and is not deleted
    const existingTag = await prisma.tag.findFirst({
      where: {
        id,
        userId, // Ensure user owns this tag
        deletedAt: null
      }
    });

    if (!existingTag) {
      throw new Error('Tag não encontrada');
    }

    // Check name uniqueness if name is being updated
    if (data.name && data.name !== existingTag.name) {
      const nameExists = await prisma.tag.findFirst({
        where: {
          name: {
            equals: data.name,
            mode: 'insensitive'
          },
          deletedAt: null,
          id: { not: id }
        }
      });

      if (nameExists) {
        throw new Error('Já existe uma tag com este nome');
      }
    }

    const updatedTag = await prisma.tag.update({
      where: { id },
      data: {
        name: data.name,
        color: data.color,
        version: { increment: 1 }
      },
      include: {
        _count: {
          select: {
            transactionTags: true
          }
        }
      }
    });

    return this.formatTag(updatedTag);
  }

  /**
   * Archive/Unarchive tag (soft delete)
   */
  async archive(id: string, archived: boolean): Promise<TagResponse> {
    const existingTag = await prisma.tag.findUnique({
      where: { id }
    });

    if (!existingTag) {
      throw new Error('Tag não encontrada');
    }

    // Check if tag is already in the desired state
    const isCurrentlyArchived = existingTag.deletedAt !== null;
    if (isCurrentlyArchived === archived) {
      const action = archived ? 'arquivada' : 'ativa';
      throw new Error(`Tag já está ${action}`);
    }

    const updatedTag = await prisma.tag.update({
      where: { id },
      data: {
        deletedAt: archived ? new Date() : null,
        version: { increment: 1 }
      },
      include: {
        _count: {
          select: {
            transactionTags: true
          }
        }
      }
    });

    return this.formatTag(updatedTag);
  }

  /**
   * Permanently delete tag
   */
  async delete(id: string): Promise<void> {
    const existingTag = await prisma.tag.findUnique({
      where: { id }
    });

    if (!existingTag) {
      throw new Error('Tag não encontrada');
    }

    // Use transaction to ensure data integrity
    await prisma.$transaction(async (tx) => {
      // Check if tag has any relationships that would prevent deletion
      const hasTransactions = await tx.transactionTag.findFirst({
        where: { tagId: id }
      });

      if (hasTransactions) {
        throw new Error('Não é possível deletar tag que possui transações associadas');
      }

      // Delete the tag
      await tx.tag.delete({
        where: { id }
      });
    });
  }

  /**
   * Get tag statistics
   */
  async getStats(userId: string): Promise<any> {
    const [total, active, archived, mostUsed] = await Promise.all([
      // Total tags
      prisma.tag.count({
        where: { userId }
      }),

      // Active tags
      prisma.tag.count({
        where: {
          userId,
          deletedAt: null
        }
      }),

      // Archived tags
      prisma.tag.count({
        where: {
          userId,
          deletedAt: { not: null }
        }
      }),

      // Most used tags (top 5)
      prisma.tag.findMany({
        where: {
          userId,
          deletedAt: null
        },
        include: {
          _count: {
            select: {
              transactionTags: true
            }
          }
        },
        orderBy: {
          transactionTags: {
            _count: 'desc'
          }
        },
        take: 5
      })
    ]);

    return {
      total,
      active,
      archived,
      mostUsed: mostUsed.map(tag => this.formatTag(tag))
    };
  }

  /**
   * Format tag data for response
   */
  private formatTag(tag: any): TagResponse {
    return {
      id: tag.id,
      name: tag.name,
      color: tag.color,
      isArchived: tag.deletedAt !== null,
      usageCount: tag._count?.transactionTags || 0,
      createdAt: tag.createdAt,
      updatedAt: tag.updatedAt,
      version: tag.version
    };
  }
}

// Export singleton instance
export const tagService = new TagService();
