#!/usr/bin/env node

/**
 * <PERSON>ript to test user data isolation
 * This script will test if users can only see their own data
 */

const axios = require('axios');

const API_BASE = 'http://localhost:3001/api/v1';

// Test users
const users = [
  {
    email: '<EMAIL>',
    password: 'password123',
    name: 'Test User'
  },
  {
    email: '<EMAIL>', 
    password: 'password123',
    name: '<PERSON>'
  }
];

async function loginUser(email, password) {
  try {
    const response = await axios.post(`${API_BASE}/auth/login`, {
      email,
      password
    });
    return response.data.data.token;
  } catch (error) {
    console.error(`❌ Failed to login ${email}:`, error.response?.data?.error?.message || error.message);
    return null;
  }
}

async function getFamilyMembers(token) {
  try {
    const response = await axios.get(`${API_BASE}/family-members`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return response.data.data;
  } catch (error) {
    console.error('❌ Failed to get family members:', error.response?.data?.error?.message || error.message);
    return [];
  }
}

async function getAccounts(token) {
  try {
    const response = await axios.get(`${API_BASE}/accounts`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return response.data.data;
  } catch (error) {
    console.error('❌ Failed to get accounts:', error.response?.data?.error?.message || error.message);
    return [];
  }
}

async function testUserIsolation() {
  console.log('🧪 Testing User Data Isolation...\n');

  // Login both users
  const tokens = {};
  for (const user of users) {
    console.log(`🔐 Logging in ${user.email}...`);
    const token = await loginUser(user.email, user.password);
    if (token) {
      tokens[user.email] = token;
      console.log(`✅ Login successful for ${user.email}`);
    } else {
      console.log(`❌ Login failed for ${user.email}`);
      return;
    }
  }

  console.log('\n📊 Testing data isolation...\n');

  // Test family members isolation
  console.log('👨‍👩‍👧‍👦 Testing Family Members isolation:');
  for (const user of users) {
    const token = tokens[user.email];
    const familyMembers = await getFamilyMembers(token);
    console.log(`   ${user.email}: ${familyMembers.length} family members`);
    if (familyMembers.length > 0) {
      console.log(`      - ${familyMembers.map(fm => fm.name).join(', ')}`);
    }
  }

  // Test accounts isolation
  console.log('\n💳 Testing Accounts isolation:');
  for (const user of users) {
    const token = tokens[user.email];
    const accounts = await getAccounts(token);
    console.log(`   ${user.email}: ${accounts.length} accounts`);
    if (accounts.length > 0) {
      console.log(`      - ${accounts.map(acc => acc.name).join(', ')}`);
    }
  }

  console.log('\n✨ User isolation test completed!');
  console.log('\n📝 Expected behavior:');
  console.log('   - Each user should only see their own family members');
  console.log('   - Each user should only see their own accounts');
  console.log('   - Data should not leak between users');
}

// Run the test
testUserIsolation().catch(console.error);
