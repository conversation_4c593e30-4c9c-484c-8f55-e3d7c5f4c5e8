import { LucideIcon } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface PageHeaderProps {
  title: string
  description: string
  icon: LucideIcon
  action?: {
    label: string
    onClick: () => void
    icon?: LucideIcon
    disabled?: boolean
    loading?: boolean
  }
}

export function PageHeader({ 
  title, 
  description, 
  icon: Icon, 
  action 
}: PageHeaderProps) {
  return (
    <div className="glass-deep p-6 rounded-2xl shadow-elegant">
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <h1 className="flex items-center gap-3 text-4xl font-bold text-gradient-deep">
            <Icon className="h-8 w-8" />
            {title}
          </h1>
          <p className="text-lg text-muted-foreground">
            {description}
          </p>
        </div>
        {action && (
          <Button
            onClick={action.onClick}
            disabled={action.disabled || action.loading}
            className="bg-gradient-deep text-white px-6 py-3 rounded-xl font-semibold shadow-glow hover:shadow-glow-lg transition-all duration-200"
          >
            {action.icon && <action.icon className="h-5 w-5 mr-2" />}
            {action.label}
          </Button>
        )}
      </div>
    </div>
  )
}
