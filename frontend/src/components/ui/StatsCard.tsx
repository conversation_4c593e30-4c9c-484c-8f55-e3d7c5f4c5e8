import { LucideIcon } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { formatCurrency } from '@/lib/utils'

interface StatsCardProps {
  title: string
  value: string | number
  icon: LucideIcon
  description?: string
  isLoading?: boolean
  isCurrency?: boolean
  colorClass?: string
  trend?: {
    value: number
    isPositive: boolean
  }
}

export function StatsCard({
  title,
  value,
  icon: Icon,
  description,
  isLoading = false,
  isCurrency = false,
  colorClass = 'bg-gradient-deep',
  trend
}: StatsCardProps) {
  if (isLoading) {
    return (
      <Card className="glass-deep shadow-elegant">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
          <CardTitle className="text-sm font-semibold text-foreground">
            <div className="h-4 w-24 bg-secondary-800 rounded animate-pulse" />
          </CardTitle>
          <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-secondary-800 animate-pulse">
            <div className="h-5 w-5 bg-secondary-700 rounded animate-pulse" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-8 w-16 bg-secondary-800 rounded animate-pulse mb-2" />
          <div className="h-4 w-32 bg-secondary-800 rounded animate-pulse" />
        </CardContent>
      </Card>
    )
  }

  const displayValue = isCurrency && typeof value === 'number' 
    ? formatCurrency(value) 
    : value

  return (
    <Card className="glass-deep shadow-elegant hover:shadow-glow transition-all duration-300">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
        <CardTitle className="text-sm font-semibold text-foreground">
          {title}
        </CardTitle>
        <div className={`flex h-10 w-10 items-center justify-center rounded-xl ${colorClass} shadow-soft`}>
          <Icon className="h-5 w-5 text-white" />
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <div>
            <div className="text-3xl font-bold text-foreground">
              {displayValue}
            </div>
            {description && (
              <p className="text-sm text-muted-foreground mt-1">
                {description}
              </p>
            )}
          </div>
          {trend && (
            <div className={`text-sm font-medium ${
              trend.isPositive ? 'text-success' : 'text-destructive'
            }`}>
              {trend.isPositive ? '+' : ''}{trend.value}%
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

interface StatsGridProps {
  children: React.ReactNode
  columns?: 2 | 3 | 4
}

export function StatsGrid({ children, columns = 3 }: StatsGridProps) {
  const gridClass = {
    2: 'grid gap-6 md:grid-cols-2',
    3: 'grid gap-6 md:grid-cols-3',
    4: 'grid gap-6 md:grid-cols-2 lg:grid-cols-4'
  }[columns]

  return (
    <div className={gridClass}>
      {children}
    </div>
  )
}
