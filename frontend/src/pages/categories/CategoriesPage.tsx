import { Tags, Plus, Folder, FolderTree, Archive } from 'lucide-react'
import { useState } from 'react'
import { useCategoryStats } from '@/hooks/useCategories'
import { CreateCategoryDialog } from '@/components/categories/CreateCategoryDialog'
import { EditCategoryDialog } from '@/components/categories/EditCategoryDialog'
import { CategoriesList } from '@/components/categories/CategoriesList'
import { PageHeader } from '@/components/ui/PageHeader'
import { StatsCard, StatsGrid } from '@/components/ui/StatsCard'
import type { Category } from '@/types/category.types'

export function CategoriesPage() {
  const [editingCategory, setEditingCategory] = useState<Category | null>(null)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [createSubcategoryParentId, setCreateSubcategoryParentId] = useState<string | null>(null)

  const { data: stats, isLoading: isLoadingStats } = useCategoryStats()

  // Handlers for category actions
  const handleEdit = (category: Category) => {
    setEditingCategory(category)
  }

  const handleCreateCategory = () => {
    setIsCreateDialogOpen(true)
  }

  const handleCreateSubcategory = (parentId: string) => {
    setCreateSubcategoryParentId(parentId)
    setIsCreateDialogOpen(true)
  }

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <PageHeader
        title="Categorias"
        description="Organize suas transações por categorias hierárquicas"
        icon={Tags}
        action={{
          label: "Nova Categoria",
          icon: Plus,
          onClick: handleCreateCategory
        }}
      />

      {/* Main Content */}
      <div className="grid gap-8">
        {/* Stats Cards */}
        <StatsGrid columns={3}>
          <StatsCard
            title="Total de Categorias"
            value={stats?.totalCategories || 0}
            icon={Tags}
            description={stats?.totalCategories === 0 ? 'Nenhuma categoria cadastrada' : 'categorias cadastradas'}
            isLoading={isLoadingStats}
            colorClass="bg-gradient-deep"
          />

          <StatsCard
            title="Categorias Principais"
            value={stats?.parentCategories || 0}
            icon={FolderTree}
            description="categorias principais"
            isLoading={isLoadingStats}
            colorClass="bg-info"
          />

          <StatsCard
            title="Categorias Ativas"
            value={(stats?.totalCategories || 0) - (stats?.archivedCategories || 0)}
            icon={Archive}
            description="categorias ativas"
            isLoading={isLoadingStats}
            colorClass="bg-success"
          />
        </StatsGrid>

        {/* Categories List */}
        <Card className="glass-deep shadow-elegant">
          <CardHeader className="pb-6">
            <CardTitle className="text-2xl font-bold text-gradient">Lista de Categorias</CardTitle>
            <CardDescription className="text-base text-muted-foreground">
              Gerencie suas categorias e subcategorias
            </CardDescription>
          </CardHeader>
          <CardContent>
            <CategoriesList
              onEdit={handleEdit}
              onCreateSubcategory={handleCreateSubcategory}
            />
          </CardContent>
        </Card>
      </div>

      {/* Create Category Dialog */}
      <CreateCategoryDialog
        open={isCreateDialogOpen}
        onOpenChange={(open) => {
          setIsCreateDialogOpen(open)
          if (!open) {
            setCreateSubcategoryParentId(null)
          }
        }}
        defaultParentId={createSubcategoryParentId || undefined}
      />

      {/* Edit Category Dialog */}
      {editingCategory && (
        <EditCategoryDialog
          category={editingCategory}
          open={!!editingCategory}
          onOpenChange={(open) => {
            if (!open) setEditingCategory(null)
          }}
        />
      )}
    </div>
  )
}
