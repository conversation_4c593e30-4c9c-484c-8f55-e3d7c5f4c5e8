import { Users, Plus, UserCheck, Archive } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'react-hot-toast'
import {
  useFamilyMemberStats,
  useArchiveFamilyMember,
  useRestoreFamilyMember,
  useDeleteFamilyMember
} from '@/hooks/useFamilyMembers'
import { FamilyMembersList } from '@/components/family/FamilyMembersList'
import { CreateFamilyMemberDialog } from '@/components/family/create-family-member-dialog'
import { EditFamilyMemberDialog } from '@/components/family/edit-family-member-dialog'
import { PageHeader } from '@/components/ui/PageHeader'
import { StatsCard, StatsGrid } from '@/components/ui/StatsCard'
import { FamilyMember } from '@/types/family-member.types'
import { useState } from 'react'

export function FamilyMembersPage() {
  const [editingMember, setEditingMember] = useState<FamilyMember | null>(null)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)

  const queryClient = useQueryClient()
  const { data: stats, isLoading: isLoadingStats } = useFamilyMemberStats()

  // Mutations for member actions
  const archiveMutation = useArchiveFamilyMember()
  const restoreMutation = useRestoreFamilyMember()
  const deleteMutation = useDeleteFamilyMember()

  // Handlers for table actions
  const handleEdit = (member: FamilyMember) => {
    setEditingMember(member)
  }

  const handleArchive = (member: FamilyMember) => {
    if (window.confirm(`Tem certeza que deseja arquivar "${member.name}"?`)) {
      archiveMutation.mutate(member.id, {
        onSuccess: () => {
          toast.success('Membro arquivado com sucesso!')
          queryClient.invalidateQueries({ queryKey: ['family-members'] })
        },
        onError: (error: any) => {
          console.error('Erro ao arquivar membro:', error)
          const message = error.response?.data?.message || 'Erro ao arquivar membro. Tente novamente.'
          toast.error(message)
        }
      })
    }
  }

  const handleRestore = (member: FamilyMember) => {
    if (window.confirm(`Tem certeza que deseja restaurar "${member.name}"?`)) {
      restoreMutation.mutate(member.id, {
        onSuccess: () => {
          toast.success('Membro restaurado com sucesso!')
          queryClient.invalidateQueries({ queryKey: ['family-members'] })
        },
        onError: (error: any) => {
          console.error('Erro ao restaurar membro:', error)
          const message = error.response?.data?.message || 'Erro ao restaurar membro. Tente novamente.'
          toast.error(message)
        }
      })
    }
  }

  const handleDelete = (member: FamilyMember) => {
    if (window.confirm(`Tem certeza que deseja excluir permanentemente "${member.name}"? Esta ação não pode ser desfeita.`)) {
      deleteMutation.mutate(member.id, {
        onSuccess: () => {
          toast.success('Membro excluído com sucesso!')
          queryClient.invalidateQueries({ queryKey: ['family-members'] })
        },
        onError: (error: any) => {
          console.error('Erro ao excluir membro:', error)
          const message = error.response?.data?.message || 'Erro ao excluir membro. Tente novamente.'
          toast.error(message)
        }
      })
    }
  }

  const handleCreateMember = () => {
    setIsCreateDialogOpen(true)
  }



  return (
    <div className="space-y-8">
      {/* Page Header */}
      <PageHeader
        title="Membros da Família"
        description="Gerencie os membros da sua família e suas informações"
        icon={Users}
        action={{
          label: "Novo Membro",
          icon: Plus,
          onClick: handleCreateMember
        }}
      />

      {/* Main Content */}
      <div className="grid gap-8">
        {/* Stats Cards */}
        <StatsGrid columns={3}>
          <StatsCard
            title="Total de Membros"
            value={stats?.totalMembers || 0}
            icon={Users}
            description={stats?.totalMembers === 0 ? 'Nenhum membro cadastrado' : 'membros cadastrados'}
            isLoading={isLoadingStats}
            colorClass="bg-gradient-deep"
          />

          <StatsCard
            title="Membros Ativos"
            value={stats?.activeMembers || 0}
            icon={UserCheck}
            description="membros não arquivados"
            isLoading={isLoadingStats}
            colorClass="bg-success"
          />

          <StatsCard
            title="Membros Arquivados"
            value={stats?.archivedMembers || 0}
            icon={Archive}
            description="membros arquivados"
            isLoading={isLoadingStats}
            colorClass="bg-warning"
          />
        </StatsGrid>

        {/* Family Members List */}
        <FamilyMembersList
          onEdit={handleEdit}
          onDelete={handleDelete}
          onArchive={handleArchive}
          onRestore={handleRestore}
        />
      </div>

      {/* Create Member Dialog */}
      <CreateFamilyMemberDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
      />

      {/* Edit Member Dialog */}
      {editingMember && (
        <EditFamilyMemberDialog
          member={editingMember}
          open={!!editingMember}
          onOpenChange={(open) => {
            if (!open) setEditingMember(null)
          }}
        />
      )}
    </div>
  )
}
