import { useState } from 'react'
import { Hash, Plus, Tag, Archive } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useTagStats } from '@/hooks/useTags'
import { TagsList } from '@/components/tags/TagsList'
import { TagModal } from '@/components/tags/TagModal'
import { PageHeader } from '@/components/ui/PageHeader'
import { StatsCard, StatsGrid } from '@/components/ui/StatsCard'
import type { TagModalState } from '@/types/tag.types'

export function TagsPage() {
  const [modalState, setModalState] = useState<TagModalState>({
    isOpen: false,
    mode: 'create',
  })

  const { data: stats, isLoading: statsLoading } = useTagStats()

  const handleCreateTag = () => {
    setModalState({
      isOpen: true,
      mode: 'create',
    })
  }

  const handleEditTag = (tag: any) => {
    setModalState({
      isOpen: true,
      mode: 'edit',
      tag,
    })
  }

  const handleCloseModal = () => {
    setModalState({
      isOpen: false,
      mode: 'create',
    })
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <PageHeader
        title="Tags"
        description="Gerencie as tags para organizar suas transações"
        icon={Hash}
        action={{
          label: "Nova Tag",
          icon: Plus,
          onClick: handleCreateTag
        }}
      />

      {/* Stats Cards */}
      <StatsGrid columns={3}>
        <StatsCard
          title="Total de Tags"
          value={(stats as any)?.data?.total || 0}
          icon={Hash}
          description="tags cadastradas no sistema"
          isLoading={statsLoading}
          colorClass="bg-gradient-deep"
        />

        <StatsCard
          title="Tags Ativas"
          value={(stats as any)?.data?.active || 0}
          icon={Tag}
          description="tags disponíveis para uso"
          isLoading={statsLoading}
          colorClass="bg-success"
        />

        <StatsCard
          title="Tags Arquivadas"
          value={(stats as any)?.data?.archived || 0}
          icon={Archive}
          description="tags removidas do uso"
          isLoading={statsLoading}
          colorClass="bg-warning"
        />
      </StatsGrid>

      {/* Tags List */}
      <Card className="glass-deep shadow-elegant">
        <CardHeader className="pb-6">
          <CardTitle className="text-2xl font-bold text-gradient">Lista de Tags</CardTitle>
          <CardDescription className="text-base text-muted-foreground">
            Visualize e gerencie todas as suas tags
          </CardDescription>
        </CardHeader>
        <CardContent>
          <TagsList onEditTag={handleEditTag} />
        </CardContent>
      </Card>

      {/* Modal */}
      <TagModal
        isOpen={modalState.isOpen}
        mode={modalState.mode}
        tag={modalState.tag}
        onClose={handleCloseModal}
      />
    </div>
  )
}
